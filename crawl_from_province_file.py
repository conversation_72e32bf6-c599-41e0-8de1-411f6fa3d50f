#!/usr/bin/env python3
"""
Script to crawl VNSDI data based on province codes from geometry_province.json
"""

import json
import os
import time
from datetime import datetime
from pathlib import Path
from crawl_vnsdi_data import VNSDIDataCrawler

def read_province_file(filename="geometry_province.json"):
    """
    Read province codes from geometry_province.json file
    
    Args:
        filename: Path to the geometry_province.json file
        
    Returns:
        List of dictionaries containing province info
    """
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        provinces = []
        if 'features' in data:
            for feature in data['features']:
                if 'attributes' in feature:
                    attrs = feature['attributes']
                    province_info = {
                        'code': attrs.get('MATINH', ''),
                        'name': attrs.get('TENTINH', ''),
                        'area': attrs.get('DIENTICH', 0),
                        'population': attrs.get('DANSO', 0)
                    }
                    provinces.append(province_info)
        
        print(f"Successfully loaded {len(provinces)} provinces from {filename}")
        return provinces
        
    except FileNotFoundError:
        print(f"Error: File {filename} not found")
        return []
    except json.JSONDecodeError as e:
        print(f"Error parsing JSON file: {e}")
        return []
    except Exception as e:
        print(f"Error reading file: {e}")
        return []

def create_output_directory(base_dir="crawled_data"):
    """
    Create output directory structure
    
    Args:
        base_dir: Base directory name for output
        
    Returns:
        Path to the created directory
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = Path(base_dir) / f"crawl_session_{timestamp}"
    
    # Create directories
    output_dir.mkdir(parents=True, exist_ok=True)
    (output_dir / "provinces").mkdir(exist_ok=True)
    (output_dir / "logs").mkdir(exist_ok=True)
    
    return output_dir

def save_province_summary(provinces, output_dir):
    """
    Save province summary to JSON file
    
    Args:
        provinces: List of province information
        output_dir: Output directory path
    """
    summary = {
        "timestamp": datetime.now().isoformat(),
        "total_provinces": len(provinces),
        "provinces": provinces
    }
    
    summary_file = output_dir / "province_summary.json"
    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump(summary, f, ensure_ascii=False, indent=2)
    
    print(f"Province summary saved to: {summary_file}")

def crawl_all_provinces(provinces, output_dir):
    """
    Crawl data for all provinces
    
    Args:
        provinces: List of province information
        output_dir: Output directory path
    """
    crawler = VNSDIDataCrawler()
    
    # Create log file
    log_file = output_dir / "logs" / "crawl_log.txt"
    
    results = {
        "timestamp": datetime.now().isoformat(),
        "total_provinces": len(provinces),
        "success_count": 0,
        "error_count": 0,
        "results": []
    }
    
    print(f"\nStarting to crawl data for {len(provinces)} provinces...")
    print(f"Output directory: {output_dir}")
    print(f"Log file: {log_file}")
    
    with open(log_file, 'w', encoding='utf-8') as log:
        log.write(f"Crawl session started at: {datetime.now().isoformat()}\n")
        log.write(f"Total provinces to process: {len(provinces)}\n\n")
        
        for i, province in enumerate(provinces, 1):
            province_code = province['code']
            province_name = province['name']
            
            print(f"\n[{i}/{len(provinces)}] Processing: {province_name} (Code: {province_code})")
            log.write(f"[{i}/{len(provinces)}] Processing: {province_name} (Code: {province_code})\n")
            
            try:
                # Crawl data
                data = crawler.get_province_data(province_code)
                
                if 'error' in data:
                    print(f"  ❌ Error: {data['error']}")
                    log.write(f"  ❌ Error: {data['error']}\n")
                    results["error_count"] += 1
                    
                    result_info = {
                        "province_code": province_code,
                        "province_name": province_name,
                        "status": "error",
                        "error": data['error'],
                        "features_count": 0
                    }
                else:
                    features_count = len(data.get('features', []))
                    print(f"  ✅ Success: {features_count} features")
                    log.write(f"  ✅ Success: {features_count} features\n")
                    results["success_count"] += 1
                    
                    # Save province data
                    province_filename = f"province_{province_code}_{province_name.replace(' ', '_').replace('/', '_')}.json"
                    province_file = output_dir / "provinces" / province_filename
                    
                    with open(province_file, 'w', encoding='utf-8') as f:
                        json.dump(data, f, ensure_ascii=False, indent=2)
                    
                    print(f"  📁 Saved to: {province_file}")
                    log.write(f"  📁 Saved to: {province_file}\n")
                    
                    result_info = {
                        "province_code": province_code,
                        "province_name": province_name,
                        "status": "success",
                        "features_count": features_count,
                        "file_path": str(province_file)
                    }
                
                results["results"].append(result_info)
                
            except Exception as e:
                print(f"  ❌ Unexpected error: {e}")
                log.write(f"  ❌ Unexpected error: {e}\n")
                results["error_count"] += 1
                
                result_info = {
                    "province_code": province_code,
                    "province_name": province_name,
                    "status": "error",
                    "error": str(e),
                    "features_count": 0
                }
                results["results"].append(result_info)
            
            # Add delay between requests to be respectful
            if i < len(provinces):
                time.sleep(0.5)
        
        log.write(f"\nCrawl session completed at: {datetime.now().isoformat()}\n")
        log.write(f"Success: {results['success_count']}, Errors: {results['error_count']}\n")
    
    # Save results summary
    results_file = output_dir / "crawl_results.json"
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n🎉 Crawl completed!")
    print(f"📊 Results: {results['success_count']} success, {results['error_count']} errors")
    print(f"📁 Results saved to: {results_file}")
    
    return results

def main():
    """
    Main function
    """
    print("=" * 60)
    print("VNSDI Data Crawler - Province File Mode")
    print("=" * 60)
    
    # Read province codes from file
    provinces = read_province_file("geometry_province.json")
    
    if not provinces:
        print("❌ No provinces found. Please check the geometry_province.json file.")
        return
    
    # Create output directory
    output_dir = create_output_directory()
    print(f"📁 Output directory created: {output_dir}")
    
    # Save province summary
    save_province_summary(provinces, output_dir)
    
    # Show province list
    print(f"\n📋 Found {len(provinces)} provinces:")
    for i, province in enumerate(provinces[:5], 1):  # Show first 5
        print(f"  {i}. {province['name']} (Code: {province['code']})")
    
    if len(provinces) > 5:
        print(f"  ... and {len(provinces) - 5} more provinces")
    
    # Ask user confirmation
    print(f"\n🚀 Ready to crawl data for {len(provinces)} provinces.")
    user_input = input("Do you want to continue? (y/n): ").lower().strip()
    
    if user_input != 'y':
        print("❌ Crawling cancelled by user.")
        return
    
    # Start crawling
    results = crawl_all_provinces(provinces, output_dir)
    
    print(f"\n📈 Final Summary:")
    print(f"  Total provinces: {results['total_provinces']}")
    print(f"  Successful: {results['success_count']}")
    print(f"  Errors: {results['error_count']}")
    print(f"  Success rate: {results['success_count']/results['total_provinces']*100:.1f}%")
    
    if results['error_count'] > 0:
        print(f"\n❌ Provinces with errors:")
        for result in results['results']:
            if result['status'] == 'error':
                print(f"  - {result['province_name']} (Code: {result['province_code']}): {result['error']}")

if __name__ == "__main__":
    main() 