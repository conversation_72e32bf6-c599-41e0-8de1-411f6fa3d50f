# Tool Crawl Dữ Liệu Geometry Xã - Task số 4

Tool này thực hiện task số 4: crawl dữ liệu geometry của xã từ API VNSDI, lưu thông tin vào bảng geo_ward.

## Cấu trúc Database

Tool sẽ truy vấn bảng `geo_ward` với các field:
- `id`: ID của ward
- `KINH_DO`: Kinh độ (longitude)
- `VI_DO`: V<PERSON> (latitude)
- `ward_title`: Tên xã/phường
- `province_title`: Tên tỉnh/thành phố

**Lưu ý**: Schema đã được cập nhật từ `ward_name` -> `ward_title`, `province_name` -> `province_title`, và loại bỏ `district_name`.

## Tổng quan

Tool sẽ:
1. Kết nối database để lấy danh sách tọa độ (KINH_DO, VI_DO) từ bảng `geo_ward`
2. Gọi API VNSDI để lấy thông tin geometry cho từng tọa độ
3. <PERSON><PERSON><PERSON> kết quả dạng JSON với thông tin chi tiết
4. Tạo individual files cho từng bản ghi với format: `ward_[ID]_[Tỉnh]_[Xã].json`

## Cấu trúc File

```
crawl_ward/
├── config.py                    # File cấu hình database và API
├── crawl_ward_geometry.py       # Script crawl chính
├── retry_failed_records.py      # Script retry
├── ward_crawler_toolkit.py      # Toolkit chính - Menu system
├── test_retry_mechanism.py      # Test retry mechanism
├── requirements.txt            # Dependencies cần thiết
├── README.md                   # File hướng dẫn này
├── RETRY_MECHANISM.md          # Tài liệu retry mechanism
├── curl.txt                    # File curl mẫu (tham khảo)
└── exports/                    # Thư mục exports nằm trong crawl_ward
    ├── sessions/               # Thư mục chứa các session
    │   └── crawl_session_YYYYMMDD_HHMMSS/
    │       ├── session_info.json
    │       ├── output/                # Thư mục kết quả (thay /results/)
    │       │   ├── ward_geometry_results.json    # File tổng hợp
    │       │   ├── failed_records.json
    │       │   ├── retry_results.json
    │       │   └── individual/        # Thư mục chứa response riêng từng bản ghi
    │       │       ├── ward_123_Hà_Nội_Xã_ABC.json
    │       │       ├── ward_456_Hà_Nội_Xã_DEF.json
    │       │       └── ...
    │       └── logs/
    │           └── crawl_ward.log
    ├── final/                  # Thư mục tổng hợp tất cả individual files
    │   └── individual/
    │       ├── ward_1_Hà_Nội_Xã_Đông_Anh.json
    │       ├── ward_2_Hà_Nội_Xã_Ba_Vì.json
    │       └── ...
    └── README.md               # Hướng dẫn thư mục exports
```

## Tính năng mới: Individual Response Files

Từ phiên bản mới, mỗi bản ghi sau khi crawl thành công sẽ được lưu thành 1 file riêng trong thư mục `output/individual/`:

### Format tên file:
- **Quy tắc**: `ward_[ID]_[Tên_Tỉnh]_[Tên_Xã].json`
- **Ví dụ**: `ward_123_Hà_Nội_Xã_ABC.json`

### Nội dung file individual:
- **Chỉ chứa API response** (raw data từ VNSDI API)
- **Không chứa metadata** (ward_id, input_coordinates, etc.)
- **Format JSON chuẩn** với indent 2 spaces

### Lợi ích:
- ✅ **Dễ debug**: Kiểm tra response riêng từng bản ghi
- ✅ **Tái sử dụng**: Sử dụng response cho các mục đích khác
- ✅ **Phân tích**: Phân tích dữ liệu từng khu vực riêng biệt
- ✅ **Backup**: Backup response raw data an toàn

## Cài đặt

1. Cài đặt dependencies:
```bash
pip install -r crawl_ward/requirements.txt
```

## Cấu hình

### Database
Trong file `config.py`, cấu hình thông tin database:
```python
DB_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': 'root',
    'database': 'urbox',
    'charset': 'utf8mb4'
}
```

### API Token
Nếu cần thay đổi token API, sửa trong `config.py`:
```python
API_CONFIG = {
    'token': 'your_new_token_here',
    # ... other config
}
```

### Tùy chỉnh khác
- `batch_size`: Số bản ghi xử lý trước khi lưu kết quả tạm thời (mặc định: 100)
- `request_delay`: Thời gian delay giữa các request (mặc định: 0.5 giây)

## Sử dụng

### Chạy Ward Crawler Toolkit:
```bash
cd crawl_ward
python ward_crawler_toolkit.py
```

### Menu chính:
```
🎯 MENU CHÍNH:
1. 🔄 Crawl dữ liệu ban đầu
2. 🔁 Retry các bản ghi failed
3. 📊 Xem báo cáo tổng quan
4. 🔧 Chọn session làm việc
5. 📁 Quản lý sessions
6. 📦 Export all individual files
0. ❌ Thoát
```

### Workflow khuyến nghị:
1. Chạy toolkit lần đầu → Tự động tạo session mới
2. Chọn option `1` để crawl dữ liệu ban đầu
3. Xem báo cáo tổng quan với option `3`
4. Nếu có failed records → Chọn option `2` để retry sau
5. Chọn option `6` để export all individual files vào thư mục chung
6. Quản lý sessions với option `5`

### Theo dõi tiến trình:
- Log sẽ hiển thị trên console và lưu vào thư mục session
- Kết quả được lưu theo từng session
- Có thể chuyển đổi giữa các session khác nhau

## Tính năng Export All Individual Files

### Mục đích:
- Tập hợp tất cả individual files từ tất cả session vào 1 thư mục chung
- Không quan tâm file được crawl từ session nào
- Tạo thư mục consolidated để sử dụng dễ dàng

### Cách sử dụng:
1. Chạy `python ward_crawler_toolkit.py` (từ trong thư mục `crawl_ward/`)
2. Chọn option `6. 📦 Export all individual files`
3. Tool sẽ tự động:
   - Tìm tất cả individual files từ tất cả session
   - Copy vào thư mục `exports/final/individual/`
   - Báo cáo thống kê chi tiết

### Kết quả:
```
crawl_ward/
└── exports/final/individual/
    ├── ward_1_Hà_Nội_Xã_Đông_Anh.json
    ├── ward_2_Hà_Nội_Xã_Ba_Vì.json
    ├── ward_3_Hồ_Chí_Minh_Xã_ABC.json
    └── ...
```

**Lưu ý**: Đường dẫn `exports/` là relative từ thư mục `crawl_ward/`, tức là từ root project sẽ là `crawl_ward/exports/`.

### Xử lý trùng lặp:
- **Phát hiện**: Tool tự động phát hiện files trùng tên
- **Xử lý**: Giữ file đầu tiên, bỏ qua file trùng lặp
- **Báo cáo**: Hiển thị số lượng files trùng lặp

### Thống kê hiển thị:
- 📁 Tổng files tìm thấy
- ✅ Files đã copy thành công
- ⚠️ Files trùng lặp đã bỏ qua
- 🎯 Thư mục đích
- 📦 Tổng files cuối cùng

## Cấu trúc dữ liệu đầu ra

File `ward_geometry_results.json` có cấu trúc:

```json
{
  "crawl_info": {
    "task": "Task 4 - Crawl dữ liệu geometry của xã",
    "timestamp": "2024-01-15T10:30:00",
    "total_records": 1500,
    "failed_records": 10,
    "success_rate": "99.33%"
  },
  "results": [
    {
      "ward_id": 123,
      "input_coordinates": {
        "longitude": 103.8702,
        "latitude": 21.3875
      },
      "ward_info": {
        "ward_title": "Xã ABC",
        "province_title": "Tỉnh DEF"
      },
      "crawl_timestamp": "2024-01-15T10:30:15",
      "api_response": { /* Raw API response */ },
      "geometry_features": [
        {
          "attributes": { /* Thông tin thuộc tính */ },
          "geometry": { /* Thông tin geometry */ }
        }
      ]
    }
  ],
  "failed_records": [
    { /* Các bản ghi crawl thất bại */ }
  ]
}
```

## Xử lý lỗi

Tool có các cơ chế xử lý lỗi:
- Retry tự động cho các lỗi network
- Lưu danh sách các bản ghi crawl thất bại
- Tự động lưu kết quả ngay cả khi có lỗi
- Log chi tiết để debug

## Lưu ý

1. Đảm bảo database đang chạy và có thể kết nối
2. Kiểm tra token API còn hiệu lực
3. Đảm bảo có đủ dung lượng disk để lưu kết quả
4. Mạng internet ổn định để gọi API

## Troubleshooting

### Lỗi kết nối database
- Kiểm tra thông tin kết nối trong `config.py`
- Đảm bảo database đang chạy
- Kiểm tra quyền truy cập

### Lỗi API
- Kiểm tra token còn hiệu lực
- Kiểm tra kết nối internet
- Xem log để biết chi tiết lỗi

### Lỗi memory
- Giảm `batch_size` trong config
- Tăng thời gian `request_delay` 