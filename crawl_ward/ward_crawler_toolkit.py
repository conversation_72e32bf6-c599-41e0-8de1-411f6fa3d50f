#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ward Crawler Toolkit - Công cụ hoàn chỉnh để crawl dữ liệu geometry xã
Task số 4: crawl dữ liệu geometry của xã, lưu thông tin vào bảng geo_ward
"""

import json
import os
import sys
import shutil
from datetime import datetime
from pathlib import Path

from config import OUTPUT_CONFIG, DB_CONFIG, API_CONFIG
from crawl_ward_geometry import WardGeometryCrawler
from retry_failed_records import RetryFailedRecords


class WardCrawlerToolkit:
    """Toolkit chính để quản lý crawl ward geometry"""
    
    def __init__(self):
        self.current_session = None
        self.sessions_dir = Path("exports/sessions")
        self.sessions_dir.mkdir(parents=True, exist_ok=True)
        
    def show_banner(self):
        """Hiển thị banner"""
        print("\n" + "="*60)
        print("🏡 WARD CRAWLER TOOLKIT")
        print("Task 4: Crawl dữ liệu geometry của xã")
        print("="*60)
        if self.current_session:
            print(f"📁 Session hiện tại: {self.current_session}")
        else:
            print("📁 Chưa chọn session")
        print("="*60)
        
    def show_menu(self):
        """Hiển thị menu chính"""
        print("\n🎯 MENU CHÍNH:")
        print("1. 🔄 Crawl dữ liệu ban đầu")
        print("2. 🔁 Retry các bản ghi failed")
        print("3. 📊 Xem báo cáo tổng quan")
        print("4. 🔧 Chọn session làm việc")
        print("5. 📁 Quản lý sessions")
        print("6. 📦 Export all individual files")
        print("7. 🗄️ Đồng bộ geometry vào database")
        print("0. ❌ Thoát")
        
    def create_session_structure(self, session_path):
        """Tạo cấu trúc thư mục cho session"""
        # Tạo thư mục con
        (session_path / "output").mkdir(parents=True, exist_ok=True)
        (session_path / "output" / "individual").mkdir(parents=True, exist_ok=True)
        (session_path / "logs").mkdir(parents=True, exist_ok=True)
        
    def create_session(self):
        """Tạo session mới"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        session_name = f"crawl_session_{timestamp}"
        session_path = self.sessions_dir / session_name
        session_path.mkdir(exist_ok=True)
        
        # Tạo cấu trúc thư mục session
        self.create_session_structure(session_path)
        
        # Tạo session info
        session_info = {
            "session_name": session_name,
            "created_at": datetime.now().isoformat(),
            "status": "active",
            "crawl_info": {
                "total_attempts": 0,
                "successful_records": 0,
                "failed_records": 0,
                "last_crawl": None
            }
        }
        
        with open(session_path / "session_info.json", 'w', encoding='utf-8') as f:
            json.dump(session_info, f, ensure_ascii=False, indent=2)
            
        self.current_session = session_name
        print(f"✅ Đã tạo session mới: {session_name}")
        return session_name
        
    def list_sessions(self):
        """Liệt kê các sessions"""
        sessions = []
        if self.sessions_dir.exists():
            for session_dir in self.sessions_dir.iterdir():
                if session_dir.is_dir():
                    info_file = session_dir / "session_info.json"
                    if info_file.exists():
                        try:
                            with open(info_file, 'r', encoding='utf-8') as f:
                                info = json.load(f)
                                sessions.append({
                                    'name': session_dir.name,
                                    'info': info,
                                    'path': session_dir
                                })
                        except Exception as e:
                            print(f"⚠️  Lỗi đọc session {session_dir.name}: {e}")
                            
        return sorted(sessions, key=lambda x: x['info']['created_at'], reverse=True)
        
    def select_session(self):
        """Chọn session để làm việc"""
        sessions = self.list_sessions()
        
        if not sessions:
            print("❌ Không có session nào. Tạo session mới...")
            self.create_session()
            return
            
        print("\n📋 Danh sách sessions:")
        for i, session in enumerate(sessions, 1):
            info = session['info']
            status = "🟢" if info['status'] == 'active' else "🔴"
            print(f"{i}. {status} {session['name']}")
            print(f"   📅 Tạo: {info['created_at']}")
            print(f"   📊 Thành công: {info['crawl_info']['successful_records']}")
            print(f"   ❌ Thất bại: {info['crawl_info']['failed_records']}")
            
        try:
            choice = input(f"\nChọn session (1-{len(sessions)}) hoặc 'n' để tạo mới: ").strip()
            
            if choice.lower() == 'n':
                self.create_session()
            elif choice.isdigit() and 1 <= int(choice) <= len(sessions):
                selected = sessions[int(choice) - 1]
                self.current_session = selected['name']
                print(f"✅ Đã chọn session: {self.current_session}")
            else:
                print("❌ Lựa chọn không hợp lệ")
                
        except KeyboardInterrupt:
            print("\n⏹️  Đã hủy")
            
    def get_session_path(self):
        """Lấy đường dẫn session hiện tại"""
        if not self.current_session:
            return None
        return self.sessions_dir / self.current_session
        
    def update_session_config(self):
        """Cập nhật config để lưu vào session hiện tại"""
        session_path = self.get_session_path()
        if not session_path:
            return
            
        # Cập nhật OUTPUT_CONFIG để lưu vào session
        OUTPUT_CONFIG['results_file'] = str(session_path / "output" / "ward_geometry_results.json")
        OUTPUT_CONFIG['log_file'] = str(session_path / "logs" / "crawl_ward.log")
        OUTPUT_CONFIG['failed_records_file'] = str(session_path / "output" / "failed_records.json")
        OUTPUT_CONFIG['retry_results_file'] = str(session_path / "output" / "retry_results.json")
        OUTPUT_CONFIG['individual_responses_dir'] = str(session_path / "output" / "individual")
        
    def crawl_initial_data(self):
        """Crawl dữ liệu ban đầu"""
        if not self.current_session:
            print("❌ Chưa chọn session. Tạo session mới...")
            self.create_session()
            
        self.update_session_config()
        
        print("\n🔄 Bắt đầu crawl dữ liệu geometry xã...")
        print("=" * 50)
        
        try:
            crawler = WardGeometryCrawler()
            crawler.crawl_all_wards()
            
            # Cập nhật session info
            self.update_session_info_after_crawl()
            
            print("\n✅ Hoàn thành crawl dữ liệu ban đầu!")
            
            # Hỏi có muốn xem báo cáo không
            if input("\n📊 Có muốn xem báo cáo không? (y/n): ").lower() == 'y':
                self.show_report()
                
        except Exception as e:
            print(f"\n❌ Lỗi khi crawl: {e}")
            
    def retry_failed_records(self):
        """Retry các bản ghi thất bại"""
        if not self.current_session:
            print("❌ Chưa chọn session")
            return
            
        self.update_session_config()
        
        # Kiểm tra có failed records không
        failed_file = Path(OUTPUT_CONFIG['failed_records_file'])
        if not failed_file.exists():
            print("✅ Không có bản ghi thất bại nào để retry")
            return
            
        with open(failed_file, 'r', encoding='utf-8') as f:
            failed_data = json.load(f)
            
        failed_count = len(failed_data.get('failed_records', []))
        if failed_count == 0:
            print("✅ Không có bản ghi thất bại nào để retry")
            return
            
        print(f"\n🔁 Tìm thấy {failed_count} bản ghi thất bại")
        print("=" * 50)
        
        # Hỏi có muốn retry không
        if input(f"Có muốn retry {failed_count} bản ghi thất bại không? (y/n): ").lower() != 'y':
            print("⏹️  Đã hủy retry")
            return
            
        try:
            retry_handler = RetryFailedRecords()
            success = retry_handler.retry_all_failed_records()
            
            if success:
                # Cập nhật session info
                self.update_session_info_after_retry()
                
                retry_handler.show_retry_summary()
                print("\n✅ Hoàn thành retry!")
                
                # Hỏi có muốn xem báo cáo không
                if input("\n📊 Có muốn xem báo cáo không? (y/n): ").lower() == 'y':
                    self.show_report()
            else:
                print("\n❌ Retry thất bại!")
                
        except Exception as e:
            print(f"\n❌ Lỗi khi retry: {e}")
            
    def update_session_info_after_crawl(self):
        """Cập nhật thông tin session sau khi crawl"""
        session_path = self.get_session_path()
        if not session_path:
            return
            
        try:
            # Đọc kết quả crawl
            results_file = Path(OUTPUT_CONFIG['results_file'])
            if results_file.exists():
                with open(results_file, 'r', encoding='utf-8') as f:
                    results = json.load(f)
                    
                # Cập nhật session info
                info_file = session_path / "session_info.json"
                with open(info_file, 'r', encoding='utf-8') as f:
                    session_info = json.load(f)
                    
                session_info['crawl_info'] = {
                    'total_attempts': results['crawl_info']['total_records'] + results['crawl_info']['failed_records'],
                    'successful_records': results['crawl_info']['total_records'],
                    'failed_records': results['crawl_info']['failed_records'],
                    'last_crawl': datetime.now().isoformat(),
                    'success_rate': results['crawl_info']['success_rate']
                }
                
                with open(info_file, 'w', encoding='utf-8') as f:
                    json.dump(session_info, f, ensure_ascii=False, indent=2)
                    
        except Exception as e:
            print(f"⚠️  Lỗi cập nhật session info: {e}")
            
    def update_session_info_after_retry(self):
        """Cập nhật thông tin session sau khi retry"""
        session_path = self.get_session_path()
        if not session_path:
            return
            
        try:
            # Đọc kết quả retry
            retry_file = Path(OUTPUT_CONFIG['retry_results_file'])
            results_file = Path(OUTPUT_CONFIG['results_file'])
            
            if retry_file.exists() and results_file.exists():
                with open(results_file, 'r', encoding='utf-8') as f:
                    results = json.load(f)
                    
                # Cập nhật session info
                info_file = session_path / "session_info.json"
                with open(info_file, 'r', encoding='utf-8') as f:
                    session_info = json.load(f)
                    
                session_info['crawl_info'] = {
                    'total_attempts': results['crawl_info']['total_records'] + results['crawl_info']['failed_records'],
                    'successful_records': results['crawl_info']['total_records'],
                    'failed_records': results['crawl_info']['failed_records'],
                    'last_retry': datetime.now().isoformat(),
                    'success_rate': results['crawl_info']['success_rate']
                }
                
                with open(info_file, 'w', encoding='utf-8') as f:
                    json.dump(session_info, f, ensure_ascii=False, indent=2)
                    
        except Exception as e:
            print(f"⚠️  Lỗi cập nhật session info: {e}")
            
    def show_report(self):
        """Hiển thị báo cáo tổng quan"""
        if not self.current_session:
            print("❌ Chưa chọn session")
            return
            
        self.update_session_config()
        
        try:
            # Đọc kết quả
            results_file = Path(OUTPUT_CONFIG['results_file'])
            if not results_file.exists():
                print("❌ Chưa có dữ liệu để báo cáo")
                return
                
            with open(results_file, 'r', encoding='utf-8') as f:
                results = json.load(f)
                
            # Hiển thị báo cáo
            print("\n" + "="*60)
            print("📊 BÁO CÁO TỔNG QUAN")
            print("="*60)
            print(f"📁 Session: {self.current_session}")
            print(f"📅 Thời gian: {results['crawl_info']['timestamp']}")
            print(f"✅ Thành công: {results['crawl_info']['total_records']} bản ghi")
            print(f"❌ Thất bại: {results['crawl_info']['failed_records']} bản ghi")
            print(f"📈 Tỷ lệ thành công: {results['crawl_info']['success_rate']}")
            
            # Hiển thị top 5 kết quả
            if results['results']:
                print(f"\n🏆 TOP 5 BẢN GHI THÀNH CÔNG:")
                for i, result in enumerate(results['results'][:5], 1):
                    ward_info = result['ward_info']
                    print(f"{i}. ID={result['ward_id']}: {ward_info['ward_title']}, {ward_info['province_title']}")
                    
            # Hiển thị failed records
            if results['failed_records']:
                print(f"\n❌ BẢN GHI THẤT BẠI:")
                for i, failed in enumerate(results['failed_records'][:5], 1):
                    print(f"{i}. ID={failed['id']}: {failed.get('ward_title', 'N/A')}")
                    
            print("="*60)
            
        except Exception as e:
            print(f"❌ Lỗi khi tạo báo cáo: {e}")
            
    def manage_sessions(self):
        """Quản lý sessions"""
        sessions = self.list_sessions()
        
        if not sessions:
            print("❌ Không có session nào")
            return
            
        print("\n📁 QUẢN LÝ SESSIONS")
        print("=" * 40)
        
        for i, session in enumerate(sessions, 1):
            info = session['info']
            status = "🟢" if info['status'] == 'active' else "🔴"
            current = "👈 HIỆN TẠI" if session['name'] == self.current_session else ""
            
            print(f"{i}. {status} {session['name']} {current}")
            print(f"   📅 Tạo: {info['created_at']}")
            print(f"   📊 Thành công: {info['crawl_info']['successful_records']}")
            print(f"   ❌ Thất bại: {info['crawl_info']['failed_records']}")
            
            # Kiểm tra kích thước file
            results_file = session['path'] / "output" / "ward_geometry_results.json"
            if results_file.exists():
                size = results_file.stat().st_size / 1024  # KB
                print(f"   💾 Kích thước: {size:.1f} KB")
            print()
            
        print("Chọn action:")
        print("1. Chọn session")
        print("2. Xóa session")
        print("0. Quay lại")
        
        try:
            choice = input("Nhập lựa chọn: ").strip()
            
            if choice == '1':
                self.select_session()
            elif choice == '2':
                self.delete_session(sessions)
            elif choice == '0':
                return
            else:
                print("❌ Lựa chọn không hợp lệ")
                
        except KeyboardInterrupt:
            print("\n⏹️  Đã hủy")
            
    def delete_session(self, sessions):
        """Xóa session"""
        try:
            choice = input(f"Chọn session để xóa (1-{len(sessions)}): ").strip()
            
            if choice.isdigit() and 1 <= int(choice) <= len(sessions):
                selected = sessions[int(choice) - 1]
                
                confirm = input(f"⚠️  Xác nhận xóa session '{selected['name']}'? (y/n): ")
                if confirm.lower() == 'y':
                    import shutil
                    shutil.rmtree(selected['path'])
                    
                    if self.current_session == selected['name']:
                        self.current_session = None
                        
                    print(f"✅ Đã xóa session: {selected['name']}")
                else:
                    print("⏹️  Đã hủy xóa")
            else:
                print("❌ Lựa chọn không hợp lệ")
                
        except Exception as e:
            print(f"❌ Lỗi khi xóa session: {e}")
            
    def consolidate_individual_files(self):
        """Tập hợp tất cả individual files từ tất cả session vào exports/final/individual/"""
        final_dir = Path("exports/final/individual")
        final_dir.mkdir(parents=True, exist_ok=True)

        print(f"\n📦 Bắt đầu tập hợp tất cả individual files...")
        print("=" * 60)

        try:
            # Tìm tất cả session
            sessions = self.list_sessions()
            if not sessions:
                print("❌ Không tìm thấy session nào.")
                return

            total_files = 0
            copied_files = 0
            duplicate_files = 0
            
            for session in sessions:
                session_path = session['path']
                individual_path = session_path / "output" / "individual"
                
                if not individual_path.exists():
                    continue
                    
                print(f"\n🔍 Kiểm tra session: {session['name']}")
                
                # Tìm tất cả individual files trong session này
                individual_files = list(individual_path.glob("*.json"))
                
                if not individual_files:
                    print(f"   ⚠️  Không có individual files")
                    continue
                    
                print(f"   📂 Tìm thấy {len(individual_files)} individual files")
                
                for individual_file in individual_files:
                    total_files += 1
                    final_file_path = final_dir / individual_file.name
                    
                    if final_file_path.exists():
                        duplicate_files += 1
                        print(f"   ⚠️  Trùng lặp: {individual_file.name}")
                    else:
                        shutil.copy2(individual_file, final_file_path)
                        copied_files += 1
                        print(f"   ✅ Copy: {individual_file.name}")

            print(f"\n" + "=" * 60)
            print(f"📊 KẾT QUẢ TẬP HỢP:")
            print(f"   📁 Tổng files tìm thấy: {total_files}")
            print(f"   ✅ Files đã copy: {copied_files}")
            print(f"   ⚠️  Files trùng lặp: {duplicate_files}")
            print(f"   🎯 Thư mục đích: {final_dir}")
            
            # Kiểm tra kết quả cuối cùng
            final_files = list(final_dir.glob("*.json"))
            print(f"   📦 Tổng files cuối cùng: {len(final_files)}")
            
            if final_files:
                print(f"\n📂 Một số file trong thư mục final:")
                for i, file_path in enumerate(sorted(final_files)[:5], 1):
                    file_size = file_path.stat().st_size
                    print(f"   {i}. {file_path.name} ({file_size:,} bytes)")
                
                if len(final_files) > 5:
                    print(f"   ... và {len(final_files) - 5} files khác")
            
            print(f"\n✅ Hoàn thành tập hợp individual files!")

        except Exception as e:
            print(f"\n❌ Lỗi khi tập hợp individual files: {e}")

    def sync_geometry_to_database(self):
        """Đồng bộ dữ liệu geometry từ individual files vào database"""
        try:
            print("\n🗄️ ĐỒNG BỘ GEOMETRY VÀO DATABASE")
            print("="*50)

            # Kiểm tra thư mục individual files
            individual_dir = Path("exports/final/individual")
            if not individual_dir.exists():
                print("❌ Không tìm thấy thư mục exports/final/individual")
                print("💡 Hãy chạy option 6 để export individual files trước")
                return

            # Lấy danh sách files
            json_files = list(individual_dir.glob("ward_*.json"))
            if not json_files:
                print("❌ Không tìm thấy file nào trong thư mục individual")
                return

            print(f"📁 Tìm thấy {len(json_files)} files individual")

            # Xác nhận từ user
            confirm = input(f"\n⚠️ Bạn có muốn đồng bộ {len(json_files)} records vào database? (y/N): ").strip().lower()
            if confirm != 'y':
                print("❌ Hủy bỏ đồng bộ")
                return

            # Import database sync class
            from database_sync import DatabaseGeometrySync

            # Tạo instance và chạy sync
            sync = DatabaseGeometrySync()
            sync.sync_all_geometry_files(individual_dir)

        except Exception as e:
            print(f"\n❌ Lỗi khi đồng bộ geometry: {e}")

    def run(self):
        """Chạy toolkit"""
        # Kiểm tra session hiện tại
        if not self.current_session:
            sessions = self.list_sessions()
            if sessions:
                print("📋 Có sessions sẵn có. Chọn session để tiếp tục...")
                self.select_session()
            else:
                print("🆕 Tạo session mới...")
                self.create_session()
                
        while True:
            try:
                self.show_banner()
                self.show_menu()
                
                choice = input("\nNhập lựa chọn: ").strip()
                
                if choice == '1':
                    self.crawl_initial_data()
                elif choice == '2':
                    self.retry_failed_records()
                elif choice == '3':
                    self.show_report()
                elif choice == '4':
                    self.select_session()
                elif choice == '5':
                    self.manage_sessions()
                elif choice == '6':
                    self.consolidate_individual_files()
                elif choice == '7':
                    self.sync_geometry_to_database()
                elif choice == '0':
                    print("👋 Bye!")
                    break
                else:
                    print("❌ Lựa chọn không hợp lệ")
                    
            except KeyboardInterrupt:
                print("\n\n👋 Bye!")
                break
            except Exception as e:
                print(f"\n❌ Lỗi: {e}")
                
                
def main():
    """Hàm main"""
    toolkit = WardCrawlerToolkit()
    toolkit.run()


if __name__ == "__main__":
    main() 