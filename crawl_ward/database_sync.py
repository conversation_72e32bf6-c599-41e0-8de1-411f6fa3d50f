#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Database Geometry Sync - Đồng bộ dữ liệu geometry vào database
"""

import json
import pymysql
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional
import re

from config import DB_CONFIG


class DatabaseGeometrySync:
    """Class để đồng bộ dữ liệu geometry vào database"""
    
    def __init__(self):
        self.connection = None
        self.cursor = None
        self.processed_count = 0
        self.success_count = 0
        self.error_count = 0
        self.errors = []
        
    def connect_database(self):
        """Kết nối database"""
        try:
            self.connection = pymysql.connect(**DB_CONFIG)
            self.cursor = self.connection.cursor()
            print("✅ Kết nối database thành công")
            return True
        except Exception as e:
            print(f"❌ Lỗi kết nối database: {e}")
            return False
            
    def close_database(self):
        """Đóng kết nối database"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
            
    def extract_ward_id_from_filename(self, filename: str) -> Optional[int]:
        """Trích xuất ward_id từ tên file"""
        # Pattern: ward_123_Province_Ward.json
        match = re.match(r'ward_(\d+)_.*\.json', filename)
        if match:
            return int(match.group(1))
        return None
        
    def load_individual_file(self, file_path: Path) -> Optional[Dict]:
        """Load dữ liệu từ individual file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return data
        except Exception as e:
            print(f"❌ Lỗi đọc file {file_path}: {e}")
            return None
            
    def extract_geometry_data(self, api_response: Dict) -> Optional[str]:
        """Trích xuất dữ liệu features từ api_response"""
        try:
            if 'features' not in api_response or not api_response['features']:
                return None

            # Lấy toàn bộ features array
            features = api_response['features']

            # Convert features về JSON string để lưu vào database
            return json.dumps(features, ensure_ascii=False)

        except Exception as e:
            print(f"❌ Lỗi trích xuất features: {e}")
            return None
            
    def update_ward_geometry(self, ward_id: int, geometry_data: str) -> bool:
        """Cập nhật geometry cho ward_id trong database"""
        try:
            # SQL update
            sql = """
                UPDATE geo_ward 
                SET geometry = %s 
                WHERE id = %s
            """
            
            self.cursor.execute(sql, (geometry_data, ward_id))
            
            # Kiểm tra có record nào được update không
            if self.cursor.rowcount > 0:
                return True
            else:
                print(f"⚠️ Không tìm thấy ward_id={ward_id} trong database")
                return False
                
        except Exception as e:
            print(f"❌ Lỗi update ward_id={ward_id}: {e}")
            return False
            
    def sync_single_file(self, file_path: Path) -> bool:
        """Đồng bộ một file individual"""
        try:
            # Trích xuất ward_id từ filename
            ward_id = self.extract_ward_id_from_filename(file_path.name)
            if not ward_id:
                print(f"❌ Không thể trích xuất ward_id từ {file_path.name}")
                return False
                
            # Load dữ liệu từ file
            data = self.load_individual_file(file_path)
            if not data:
                return False
                
            # Kiểm tra có api_response không
            if 'api_response' not in data:
                print(f"❌ File {file_path.name} không có api_response")
                return False
                
            # Trích xuất features từ api_response
            features_data = self.extract_geometry_data(data['api_response'])
            if not features_data:
                print(f"❌ File {file_path.name} không có features data")
                return False

            # Update features vào database
            success = self.update_ward_geometry(ward_id, features_data)
            
            if success:
                print(f"✅ Đồng bộ thành công ward_id={ward_id}")
                self.success_count += 1
            else:
                self.error_count += 1
                self.errors.append(f"ward_id={ward_id}: Update failed")
                
            return success
            
        except Exception as e:
            print(f"❌ Lỗi sync file {file_path.name}: {e}")
            self.error_count += 1
            self.errors.append(f"{file_path.name}: {str(e)}")
            return False
            
    def sync_all_geometry_files(self, individual_dir: Path):
        """Đồng bộ tất cả geometry files"""
        try:
            print(f"\n🚀 Bắt đầu đồng bộ geometry...")
            start_time = datetime.now()
            
            # Kết nối database
            if not self.connect_database():
                return
                
            # Lấy danh sách files
            json_files = list(individual_dir.glob("ward_*.json"))
            total_files = len(json_files)
            
            print(f"📁 Tổng số files: {total_files}")
            print("="*50)
            
            # Xử lý từng file
            for i, file_path in enumerate(json_files, 1):
                print(f"\n[{i}/{total_files}] Xử lý {file_path.name}...")
                self.processed_count += 1
                
                # Sync file
                self.sync_single_file(file_path)
                
                # Commit sau mỗi 10 files
                if i % 10 == 0:
                    self.connection.commit()
                    print(f"💾 Committed {i} files")
                    
            # Commit cuối cùng
            self.connection.commit()
            
            # Báo cáo kết quả
            end_time = datetime.now()
            duration = end_time - start_time
            
            print("\n" + "="*50)
            print("📊 KẾT QUẢ ĐỒNG BỘ")
            print("="*50)
            print(f"⏱️ Thời gian: {duration}")
            print(f"📁 Tổng files: {self.processed_count}")
            print(f"✅ Thành công: {self.success_count}")
            print(f"❌ Lỗi: {self.error_count}")
            
            if self.errors:
                print(f"\n📋 CHI TIẾT LỖI:")
                for error in self.errors[:10]:  # Hiển thị tối đa 10 lỗi
                    print(f"  - {error}")
                if len(self.errors) > 10:
                    print(f"  ... và {len(self.errors) - 10} lỗi khác")
                    
        except Exception as e:
            print(f"\n❌ Lỗi trong quá trình đồng bộ: {e}")
            
        finally:
            # Đóng kết nối
            self.close_database()
            print(f"\n🔒 Đã đóng kết nối database")


def main():
    """Test function"""
    sync = DatabaseGeometrySync()
    individual_dir = Path("exports/final/individual")
    sync.sync_all_geometry_files(individual_dir)


if __name__ == "__main__":
    main()
