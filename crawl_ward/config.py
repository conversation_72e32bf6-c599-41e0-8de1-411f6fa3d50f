# Configuration file for ward geometry crawler

# Database configuration
DB_CONFIG = {
    'host': '127.0.0.1',
    'port': 3306,
    'user': 'root',
    'password': 'root',
    'database': 'urbox',
    'charset': 'utf8mb4'
}

# API configuration
API_CONFIG = {
    'base_url': 'https://vnsdi.mae.gov.vn/basemap/rest/services/TraCuuDVHC/MapServer/1/query',
    'token': "ExStvSdFXfM9AqU46uMern9qr2wUUi-i_ks01eTOCqyM-Ckh6nCDxhsqmS05JaFqzUSipH3bzYU7hEGDZVRaBA..",
    'headers': {
        'accept': '*/*',
        'accept-language': 'vi-VN,vi;q=0.9,en-US;q=0.8,en;q=0.7',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:132.0) Gecko/20100101 Firefox/132.0',
        # Security headers for CORS and browser compatibility
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        # Strong cache-busting headers to avoid cached responses
        'Cache-Control': 'no-cache, no-store, must-revalidate, max-age=0',
        'Pragma': 'no-cache',
        'Expires': '0',
        'X-Requested-With': 'XMLHttpRequest',
        # Cookie: REMOVED - this was causing cache issues where all coordinates returned same cached result
        # Original Cookie caused server to return "Tỉnh Cà Mau" for all spatial queries
        # Without Cookie, API works correctly and returns proper spatial data
    },
    'params': {
        'returnGeometry': 'true',
        'where': '1=1',
        'outSR': '4326',
        'outFields': '*',
        'inSR': '4326',
        'geometryType': 'esriGeometryPoint',
        'spatialRel': 'esriSpatialRelWithin',
        'f': 'json'
    }
}

# Output configuration
OUTPUT_CONFIG = {
    'results_file': 'exports/ward_geometry_results.json',
    'log_file': 'exports/crawl_ward.log',
    'failed_records_file': 'exports/failed_records.json',
    'retry_results_file': 'exports/retry_results.json',
    'individual_responses_dir': 'exports/individual_responses',  # Thư mục cho individual files
    'batch_size': 100,  # Number of records to process in each batch
    'request_delay': 2.0,  # Delay between requests (seconds)
    'max_retries': 3,  # Maximum retry attempts for failed records
    'retry_delay': 1.0  # Delay between retry attempts (seconds)
} 