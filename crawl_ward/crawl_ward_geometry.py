#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Tool crawl dữ liệu geometry của xã từ API VNSDI
Task số 4: crawl dữ liệu geometry của xã, lưu thông tin vào bảng geo_ward
"""

import json
import logging
import time
import urllib.parse
import re
from datetime import datetime
from decimal import Decimal
from typing import Any, Dict, List, Optional, Tuple
from pathlib import Path

import pymysql
import pymysql.cursors
import requests
from config import API_CONFIG, DB_CONFIG, OUTPUT_CONFIG


class WardGeometryCrawler:
    """Class để crawl dữ liệu geometry của xã"""
    
    def __init__(self):
        self.setup_logging()
        # Bỏ session để tránh cache issues - dùng requests.get() trực tiếp
        self.results = []
        self.failed_records = []
        
    def setup_logging(self):
        """Thiết lập logging"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(OUTPUT_CONFIG['log_file'], encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def connect_database(self) -> pymysql.Connection:
        """Kết nối đến database"""
        try:
            connection = pymysql.connect(**DB_CONFIG)
            self.logger.info("Đã kết nối thành công đến database")
            return connection
        except Exception as e:
            self.logger.error(f"Lỗi kết nối database: {e}")
            raise
            
    def get_ward_coordinates(self) -> List[Dict[str, Any]]:
        """Lấy danh sách tọa độ từ bảng geo_ward"""
        connection = self.connect_database()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # Lấy tất cả tọa độ từ bảng geo_ward
                query = """
                SELECT id, KINH_DO as longitude, VI_DO as latitude, 
                       ward_title, province_title
                FROM geo_ward 
                WHERE KINH_DO IS NOT NULL AND VI_DO IS NOT NULL
                  AND KINH_DO != '' AND VI_DO != ''
                ORDER BY id limit 1
                """
                cursor.execute(query)
                records = cursor.fetchall()
                
                # Convert VARCHAR coordinates to float
                for record in records:
                    try:
                        record['longitude'] = float(record['longitude'])
                        record['latitude'] = float(record['latitude'])
                    except (ValueError, TypeError):
                        self.logger.warning(f"Không thể chuyển đổi tọa độ cho record ID {record['id']}")
                        continue
                
                self.logger.info(f"Đã lấy được {len(records)} bản ghi từ bảng geo_ward")
                return list(records)
                
        except Exception as e:
            self.logger.error(f"Lỗi khi lấy dữ liệu từ database: {e}")
            raise
        finally:
            connection.close()
            
    def build_geometry_param(self, longitude: float, latitude: float) -> str:
        """Tạo parameter geometry cho API call"""
        geometry_dict = {
            "x": longitude,
            "y": latitude,
            "spatialReference": {"wkid": 4326}
        }
        return urllib.parse.quote(json.dumps(geometry_dict))
        
    def sanitize_filename(self, text: str) -> str:
        """Làm sạch tên file, loại bỏ ký tự đặc biệt"""
        if not text:
            return "Unknown"
        # Thay thế các ký tự đặc biệt bằng underscore
        text = re.sub(r'[^\w\s-]', '', text)  # Loại bỏ ký tự đặc biệt
        text = re.sub(r'[\s_]+', '_', text)   # Thay khoảng trắng bằng underscore
        text = text.strip('_')                 # Loại bỏ underscore đầu cuối
        return text[:50]  # Giới hạn độ dài
        
    def create_individual_filename(self, record: Dict) -> str:
        """Tạo tên file individual theo format: ward_[ID]_[Tên_Tỉnh]_[Tên_Xã].json"""
        ward_id = record.get('id', 'unknown')
        province_name = self.sanitize_filename(record.get('province_title', 'Unknown_Province'))
        ward_name = self.sanitize_filename(record.get('ward_title', 'Unknown_Ward'))
        
        filename = f"ward_{ward_id}_{province_name}_{ward_name}.json"
        return filename
        
    def save_individual_response(self, record: Dict, api_response: Dict, request_params: Dict = None):
        """Lưu response của từng bản ghi vào file riêng"""
        try:
            # Tạo thư mục individual nếu chưa có
            individual_dir = Path(OUTPUT_CONFIG['individual_responses_dir'])
            individual_dir.mkdir(parents=True, exist_ok=True)

            # Tạo tên file
            filename = self.create_individual_filename(record)
            filepath = individual_dir / filename

            # Tạo data để lưu bao gồm cả request và response
            data_to_save = {
                'ward_info': {
                    'ward_id': record.get('id'),
                    'ward_title': record.get('ward_title'),
                    'province_title': record.get('province_title'),
                    'coordinates': {
                        'longitude': record.get('longitude'),
                        'latitude': record.get('latitude')
                    }
                },
                'request_details': {
                    'base_url': API_CONFIG['base_url'],
                    'full_url': request_params.get('full_url') if request_params else None,
                    'params': request_params,
                    'headers': API_CONFIG['headers'].copy(),
                    'timestamp': datetime.now().isoformat()
                },
                'api_response': api_response
            }

            # Lưu data vào file
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data_to_save, f, ensure_ascii=False, indent=2)

            self.logger.info(f"Đã lưu individual response với request details: {filename}")

        except Exception as e:
            self.logger.error(f"Lỗi khi lưu individual response cho ward_id={record.get('id')}: {e}")
            
    def call_vnsdi_api(self, longitude: float, latitude: float) -> Optional[List[Dict]]:
        """Gọi API VNSDI để lấy thông tin geometry"""
        try:
            # Tạo parameters cho API call
            params = API_CONFIG['params'].copy()
            params['geometry'] = self.build_geometry_param(longitude, latitude)
            params['token'] = API_CONFIG['token']

            # Thêm timestamp để cache-busting
            import time
            import hashlib
            import random

            timestamp = int(time.time() * 1000)  # milliseconds
            params['_timestamp'] = timestamp
            params['_random'] = hashlib.md5(f"{timestamp}{random.random()}".encode()).hexdigest()[:8]

            # Tạo headers từ config với dynamic cache-busting
            headers = API_CONFIG['headers'].copy()
            headers['If-None-Match'] = f'"{timestamp}"'
            headers['X-Cache-Buster'] = f"{timestamp}-{random.randint(1000, 9999)}"
            headers['X-Request-ID'] = hashlib.md5(f"{timestamp}{longitude}{latitude}".encode()).hexdigest()[:16]

            # Debug logging - log headers trước khi gửi
            self.logger.info(f"=== DEBUG REQUEST cho tọa độ ({longitude}, {latitude}) ===")
            self.logger.info(f"URL: {API_CONFIG['base_url']}")
            self.logger.info(f"Using direct requests.get() - no session cache")
            self.logger.info(f"Headers will be sent:")
            for key, value in headers.items():
                self.logger.info(f"  {key}: {value}")

            # Thêm delay trước mỗi request để tránh cache
            self.logger.info(f"Đang chờ {OUTPUT_CONFIG['request_delay']}s để tránh cache...")
            time.sleep(OUTPUT_CONFIG['request_delay'])

            # Tạo URL với params được encode trực tiếp
            import urllib.parse
            query_string = urllib.parse.urlencode(params)
            full_url = f"{API_CONFIG['base_url']}?{query_string}"

            # Gọi API trực tiếp với URL đầy đủ - không dùng params
            response = requests.request("GET",
                full_url,
                headers=headers,
                data={},
                timeout=30
            )
            
            # Debug logging - log actual request
            self.logger.info(f"=== ACTUAL REQUEST SENT ===")
            self.logger.info(f"Constructed URL: {full_url}")
            self.logger.info(f"Final URL: {response.request.url}")
            self.logger.info(f"Request headers actually sent:")
            for key, value in response.request.headers.items():
                self.logger.info(f"  {key}: {value}")
            
            if response.status_code == 200:
                data = response.json()
                
                # Kiểm tra response có lỗi không
                if 'error' in data:
                    self.logger.warning(f"API trả về lỗi cho tọa độ ({longitude}, {latitude}): {data['error']}")
                    return [params,{}]
                    
                self.logger.info(f"✅ API call thành công cho tọa độ ({longitude}, {latitude})")
                # Thêm full URL vào params để lưu vào kết quả
                params['full_url'] = full_url
                return [params,data]
            else:
                self.logger.warning(f"API call failed với status {response.status_code} cho tọa độ ({longitude}, {latitude})")
                self.logger.warning(f"Response: {response.text}")
                return [params,{}]
                
        except Exception as e:
            self.logger.error(f"Lỗi khi gọi API cho tọa độ ({longitude}, {latitude}): {e}")
            return [None, {}]
            
    def convert_decimal_to_float(self, obj):
        """Convert Decimal objects to float for JSON serialization"""
        if isinstance(obj, Decimal):
            return float(obj)
        elif isinstance(obj, dict):
            return {k: self.convert_decimal_to_float(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self.convert_decimal_to_float(item) for item in obj]
        return obj
        
    def process_api_response(self, record: Dict, api_response: Dict, request_params: Dict = None) -> Dict:
        """Xử lý response từ API và tạo kết quả"""
        result = {
            'ward_id': record['id'],
            'input_coordinates': {
                'longitude': float(record['longitude']) if isinstance(record['longitude'], Decimal) else record['longitude'],
                'latitude': float(record['latitude']) if isinstance(record['latitude'], Decimal) else record['latitude']
            },
            'ward_info': {
                'ward_title': record.get('ward_title'),
                'province_title': record.get('province_title')
            },
            'crawl_timestamp': datetime.now().isoformat(),
            'request_details': {
                'base_url': API_CONFIG['base_url'],
                'full_url': request_params.get('full_url') if request_params else None,
                'params': request_params,
                'headers': API_CONFIG['headers'].copy() if request_params else None
            },
            'api_response': api_response,
            'geometry_features': []
        }
        
        # Xử lý features từ API response
        if 'features' in api_response and api_response['features']:
            for feature in api_response['features']:
                geometry_info = {
                    'attributes': feature.get('attributes', {}),
                    'geometry': feature.get('geometry', {})
                }
                result['geometry_features'].append(geometry_info)
                
        return result
        
    def crawl_single_record(self, record: Dict) -> bool:
        """Crawl dữ liệu cho một bản ghi"""
        try:
            longitude = float(record['longitude'])
            latitude = float(record['latitude'])
            
            self.logger.info(f"Đang crawl dữ liệu cho ward_id={record['id']}, tọa độ=({longitude}, {latitude})")
            
            # Gọi API
            params, api_response = self.call_vnsdi_api(longitude, latitude)
            self.logger.info(f"params: {params}")

            # Kiểm tra kết quả API call
            if params is not None and api_response and api_response != {}:
                # Lưu individual response vào file riêng với request details
                self.save_individual_response(record, api_response, params)

                # Xử lý kết quả với request details
                result = self.process_api_response(record, api_response, params)
                self.results.append(result)

                self.logger.info(f"Crawl thành công ward_id={record['id']}")
                return True
            else:
                # Lưu failed record với request details để debug
                failed_record = record.copy()
                failed_record['request_details'] = {
                    'url': API_CONFIG['base_url'],
                    'params': params,
                    'headers': API_CONFIG['headers'].copy(),
                    'failure_reason': 'API call failed or returned empty response'
                }
                self.failed_records.append(failed_record)
                self.logger.warning(f"Crawl thất bại ward_id={record['id']}")
                return False
                
        except Exception as e:
            self.logger.error(f"Lỗi khi crawl ward_id={record['id']}: {e}")
            self.failed_records.append(record)
            return False
            
    def save_results(self):
        """Lưu kết quả crawl vào file JSON"""
        try:
            # Tạo cấu trúc dữ liệu output
            output_data = {
                'crawl_info': {
                    'task': 'Task 4 - Crawl dữ liệu geometry của xã',
                    'timestamp': datetime.now().isoformat(),
                    'total_records': len(self.results),
                    'failed_records': len(self.failed_records),
                    'success_rate': f"{(len(self.results) / (len(self.results) + len(self.failed_records)) * 100):.2f}%" if (len(self.results) + len(self.failed_records)) > 0 else "0%"
                },
                'results': self.results,
                'failed_records': self.failed_records
            }
            
            # Convert Decimal objects to float before saving
            output_data_converted = self.convert_decimal_to_float(output_data)
            
            # Lưu file JSON
            with open(OUTPUT_CONFIG['results_file'], 'w', encoding='utf-8') as f:
                json.dump(output_data_converted, f, ensure_ascii=False, indent=2)
                
            # Lưu failed records vào file riêng để retry
            if self.failed_records:
                self.save_failed_records()
                
            self.logger.info(f"Đã lưu kết quả vào file: {OUTPUT_CONFIG['results_file']}")
            self.logger.info(f"Tổng số bản ghi thành công: {len(self.results)}")
            self.logger.info(f"Tổng số bản ghi thất bại: {len(self.failed_records)}")
            
        except Exception as e:
            self.logger.error(f"Lỗi khi lưu kết quả: {e}")
            raise
            
    def save_failed_records(self):
        """Lưu danh sách các bản ghi crawl thất bại để retry"""
        try:
            failed_data = {
                'failed_info': {
                    'timestamp': datetime.now().isoformat(),
                    'total_failed': len(self.failed_records),
                    'source': 'main_crawl'
                },
                'failed_records': self.convert_decimal_to_float(self.failed_records)
            }
            
            with open(OUTPUT_CONFIG['failed_records_file'], 'w', encoding='utf-8') as f:
                json.dump(failed_data, f, ensure_ascii=False, indent=2)
                
            self.logger.info(f"Đã lưu {len(self.failed_records)} bản ghi thất bại vào: {OUTPUT_CONFIG['failed_records_file']}")
            
        except Exception as e:
            self.logger.error(f"Lỗi khi lưu failed records: {e}")
            
    def load_failed_records(self):
        """Tải danh sách failed records từ file"""
        try:
            with open(OUTPUT_CONFIG['failed_records_file'], 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data.get('failed_records', [])
        except FileNotFoundError:
            self.logger.warning(f"Không tìm thấy file failed records: {OUTPUT_CONFIG['failed_records_file']}")
            return []
        except Exception as e:
            self.logger.error(f"Lỗi khi tải failed records: {e}")
            return []
            
    def merge_retry_results(self, retry_results_file):
        """Merge kết quả retry vào file kết quả chính"""
        try:
            # Tải kết quả retry
            with open(retry_results_file, 'r', encoding='utf-8') as f:
                retry_data = json.load(f)
            
            # Tải kết quả chính
            with open(OUTPUT_CONFIG['results_file'], 'r', encoding='utf-8') as f:
                main_data = json.load(f)
            
            # Merge results
            main_data['results'].extend(retry_data.get('results', []))
            
            # Cập nhật failed_records (loại bỏ những bản ghi đã retry thành công)
            retry_success_ids = {r['ward_id'] for r in retry_data.get('results', [])}
            main_data['failed_records'] = [
                r for r in main_data['failed_records'] 
                if r.get('id') not in retry_success_ids
            ]
            
            # Thêm failed records mới từ retry
            main_data['failed_records'].extend(retry_data.get('failed_records', []))
            
            # Cập nhật crawl_info
            main_data['crawl_info']['total_records'] = len(main_data['results'])
            main_data['crawl_info']['failed_records'] = len(main_data['failed_records'])
            total = main_data['crawl_info']['total_records'] + main_data['crawl_info']['failed_records']
            main_data['crawl_info']['success_rate'] = f"{(main_data['crawl_info']['total_records'] / total * 100):.2f}%" if total > 0 else "0%"
            main_data['crawl_info']['last_updated'] = datetime.now().isoformat()
            
            # Lưu lại file chính
            with open(OUTPUT_CONFIG['results_file'], 'w', encoding='utf-8') as f:
                json.dump(main_data, f, ensure_ascii=False, indent=2)
                
            self.logger.info(f"Đã merge {len(retry_data.get('results', []))} kết quả retry vào file chính")
            
        except Exception as e:
            self.logger.error(f"Lỗi khi merge retry results: {e}")
            raise
            
    def crawl_all_wards(self):
        """Crawl dữ liệu cho tất cả các xã"""
        try:
            self.logger.info("Bắt đầu crawl dữ liệu geometry của xã...")
            
            # Lấy danh sách tọa độ từ database
            ward_records = self.get_ward_coordinates()
            
            if not ward_records:
                self.logger.warning("Không có dữ liệu để crawl")
                return
                
            total_records = len(ward_records)
            self.logger.info(f"Sẽ crawl {total_records} bản ghi")
            
            # Crawl từng bản ghi
            for i, record in enumerate(ward_records, 1):
                self.logger.info(f"Đang xử lý bản ghi {i}/{total_records}")
                
                # Crawl dữ liệu
                self.crawl_single_record(record)
                
                # Delay giữa các request
                if i < total_records:
                    time.sleep(OUTPUT_CONFIG['request_delay'])
                    
                # Lưu kết quả tạm thời sau mỗi batch
                if i % OUTPUT_CONFIG['batch_size'] == 0:
                    self.logger.info(f"Đã hoàn thành {i} bản ghi, lưu kết quả tạm thời...")
                    self.save_results()
                    
            # Lưu kết quả cuối cùng
            self.save_results()
            self.logger.info("Hoàn thành crawl dữ liệu!")
            
            # Thông báo failed records (không retry ngay)
            if self.failed_records:
                self.logger.info(f"💡 Có {len(self.failed_records)} bản ghi thất bại. Có thể retry sau bằng option 'Retry các bản ghi failed'")
            
        except Exception as e:
            self.logger.error(f"Lỗi trong quá trình crawl: {e}")
            # Vẫn cố gắng lưu kết quả đã có
            if self.results:
                self.save_results()
            raise


def main():
    """Hàm main"""
    crawler = WardGeometryCrawler()
    try:
        crawler.crawl_all_wards()
    except KeyboardInterrupt:
        crawler.logger.info("Đã dừng crawl theo yêu cầu người dùng")
        if crawler.results:
            crawler.save_results()
    except Exception as e:
        crawler.logger.error(f"Lỗi chương trình: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main()) 