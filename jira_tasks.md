# JIRA TASKS - VNSDI Data Migration Project

## Task 4: Crawl Geometry Data for Ward Level
**Type**: Task  
**Priority**: Medium  
**Status**: To Do  
**Assignee**: [To be assigned]  

### Description
Crawl dữ liệu geometry của xã, phường và lưu thông tin vào bảng geo_ward

### Acceptance Criteria
- [ ] Crawl được geometry data cho tất cả các xã/phường
- [ ] Dữ liệu geometry được lưu chính xác vào bảng geo_ward
- [ ] Đảm bảo tính toàn vẹn dữ liệu (không bị thiếu geometry cho xã/phường nào)
- [ ] Có logging chi tiết quá trình crawl
- [ ] Có mechanism retry cho các trường hợp crawl thất bại

---

## Task 5: Update Province Table Structure
**Type**: Task  
**Priority**: High  
**Status**: To Do  
**Assignee**: [To be assigned]  

### Description
Cập nhật bảng province để chứa tỉnh cũ và mới, thêm cột new_province_id để lưu ID của tỉnh mới ở các tỉnh thành cũ. Tỉnh mới và cũ được phân biệt bởi cột is_merge = 2 (tỉnh mới)

### Acceptance Criteria
- [ ] Thêm cột new_province_id vào bảng province
- [ ] Cập nhật cột is_merge: 2 cho tỉnh mới
- [ ] Map đúng relationship giữa tỉnh cũ và tỉnh mới
- [ ] Ví dụ: Ninh Bình mới được hợp thành từ 3 tỉnh: Ninh Bình, Hà Nam, Nam Định
- [ ] Đảm bảo data integrity khi migration
- [ ] Tạo script migration và rollback

---

## Task 6: Update Ward Table Structure
**Type**: Task  
**Priority**: High  
**Status**: To Do  
**Assignee**: [To be assigned]  

### Description
Cập nhật bảng ward chứa xã cũ và mới, tương tự như task 5. Tuy nhiên sẽ không có xã nào giữ nguyên mà sẽ được hợp nhất lại thành xã lớn hơn, cần biết xã mới được hợp thành từ các xã cũ nào

### Acceptance Criteria
- [ ] Thêm cột new_ward_id vào bảng ward
- [ ] Cập nhật cột is_merge để phân biệt xã cũ và xã mới
- [ ] Map đúng relationship giữa xã cũ và xã mới
- [ ] Đảm bảo tất cả xã cũ đều có xã mới tương ứng
- [ ] Tạo script migration và rollback
- [ ] Kiểm tra data integrity

---

## Task 7: Synchronize Brand Office Coordinates
**Type**: Task  
**Priority**: Medium  
**Status**: To Do  
**Assignee**: [To be assigned]  

### Description
Đồng bộ lại dữ liệu lat, long cho bảng brand_office để đảm bảo tính chính xác

### Acceptance Criteria
- [ ] Kiểm tra và validate tất cả tọa độ lat, long hiện tại
- [ ] Cập nhật các tọa độ sai lệch hoặc thiếu
- [ ] Sử dụng geocoding service để lấy tọa độ chính xác
- [ ] Backup dữ liệu trước khi cập nhật
- [ ] Log chi tiết các thay đổi
- [ ] Validate tọa độ nằm trong phạm vi Việt Nam

---

## Task 8: Synchronize Brand Store Coordinates
**Type**: Task  
**Priority**: Medium  
**Status**: To Do  
**Assignee**: [To be assigned]  

### Description
Đồng bộ lại dữ liệu lat, long cho bảng brand_store để đảm bảo tính chính xác

### Acceptance Criteria
- [ ] Kiểm tra và validate tất cả tọa độ lat, long hiện tại
- [ ] Cập nhật các tọa độ sai lệch hoặc thiếu
- [ ] Sử dụng geocoding service để lấy tọa độ chính xác
- [ ] Backup dữ liệu trước khi cập nhật
- [ ] Log chi tiết các thay đổi
- [ ] Validate tọa độ nằm trong phạm vi Việt Nam

---

## Task 9: Update Brand Office Address Using Geometry
**Type**: Task  
**Priority**: Medium  
**Status**: To Do  
**Assignee**: [To be assigned]  

### Description
Sau khi đã đồng bộ lại dữ liệu lat,long, dựa vào dữ liệu geometry lấy được ở task 3, sử dụng thư viện để xem tọa độ của bản ghi thuộc địa phận xã, phường nào, sau đó cập nhật lại vào cột title. Chỉ cập nhật lại xã phường và tỉnh thành, bỏ quận, huyện. Giữ nguyên số nhà, tên đường, khu dân cư.

### Acceptance Criteria
- [ ] Sử dụng geometry data để xác định xã/phường từ tọa độ
- [ ] Cập nhật cột title với format: [số nhà, tên đường, khu dân cư], [xã/phường], [tỉnh/thành phố]
- [ ] Loại bỏ thông tin quận/huyện
- [ ] Sử dụng dữ liệu tỉnh/xã mới từ task 5,6
- [ ] Backup dữ liệu trước khi cập nhật
- [ ] Log chi tiết các thay đổi
- [ ] Xử lý các trường hợp không tìm được geometry match

---

## Task 10: Update Brand Store Address Using Geometry
**Type**: Task  
**Priority**: Medium  
**Status**: To Do  
**Assignee**: [To be assigned]  

### Description
Thực hiện task tương tự như task 9 nhưng cho bảng brand_store

### Acceptance Criteria
- [ ] Sử dụng geometry data để xác định xã/phường từ tọa độ
- [ ] Cập nhật cột title với format: [số nhà, tên đường, khu dân cư], [xã/phường], [tỉnh/thành phố]
- [ ] Loại bỏ thông tin quận/huyện
- [ ] Sử dụng dữ liệu tỉnh/xã mới từ task 5,6
- [ ] Backup dữ liệu trước khi cập nhật
- [ ] Log chi tiết các thay đổi
- [ ] Xử lý các trường hợp không tìm được geometry match

---

## Task 11: Add New Data Flag to Gift Receiver Table
**Type**: Task  
**Priority**: Low  
**Status**: To Do  
**Assignee**: [To be assigned]  

### Description
Thêm cột để đánh dấu sử dụng dữ liệu mới (hoặc có thể đánh dấu từ ID hoặc created nào sẽ sử dụng dữ liệu mới)

### Acceptance Criteria
- [ ] Thêm cột flag để đánh dấu record sử dụng dữ liệu mới
- [ ] Xác định cutoff point (ID hoặc created date) để phân biệt
- [ ] Cập nhật flag cho tất cả record hiện tại
- [ ] Tạo script migration
- [ ] Cập nhật application logic để handle flag mới

---

## Task 12: Add New Data Flag to Address Table
**Type**: Task  
**Priority**: Low  
**Status**: To Do  
**Assignee**: [To be assigned]  

### Description
Thêm task tương tự như task 11 cho bảng address

### Acceptance Criteria
- [ ] Thêm cột flag để đánh dấu record sử dụng dữ liệu mới
- [ ] Xác định cutoff point (ID hoặc created date) để phân biệt
- [ ] Cập nhật flag cho tất cả record hiện tại
- [ ] Tạo script migration
- [ ] Cập nhật application logic để handle flag mới

---

## Task 13: Analyze Cart Table Requirements
**Type**: Research  
**Priority**: Low  
**Status**: To Do  
**Assignee**: [To be assigned]  

### Description
Đang phân tích requirements cho bảng cart

### Acceptance Criteria
- [ ] Phân tích impact của thay đổi dữ liệu địa chỉ lên bảng cart
- [ ] Xác định các trường cần cập nhật
- [ ] Đề xuất solution và timeline
- [ ] Tạo technical specification
- [ ] Estimate effort cho implementation

---

## Task 14: Analyze Cart Detail Table Requirements
**Type**: Research  
**Priority**: Low  
**Status**: To Do  
**Assignee**: [To be assigned]  

### Description
Đang phân tích requirements cho bảng cart_detail

### Acceptance Criteria
- [ ] Phân tích impact của thay đổi dữ liệu địa chỉ lên bảng cart_detail
- [ ] Xác định các trường cần cập nhật
- [ ] Đề xuất solution và timeline
- [ ] Tạo technical specification
- [ ] Estimate effort cho implementation

---

## Task 15: API - Get New Province List
**Type**: Story  
**Priority**: High  
**Status**: To Do  
**Assignee**: [To be assigned]  

### Description
Viết API lấy danh sách tỉnh mới

### Acceptance Criteria
- [ ] Tạo endpoint GET /api/provinces/new
- [ ] Trả về danh sách tỉnh mới (is_merge = 2)
- [ ] Bao gồm thông tin: id, name, code, geometry (optional)
- [ ] Implement pagination
- [ ] Implement caching
- [ ] Viết unit tests
- [ ] Viết API documentation
- [ ] Handle error cases

---

## Task 16: API - Get Ward List by Province
**Type**: Story  
**Priority**: High  
**Status**: To Do  
**Assignee**: [To be assigned]  

### Description
Viết API lấy danh sách xã dựa trên ID tỉnh

### Acceptance Criteria
- [ ] Tạo endpoint GET /api/provinces/{province_id}/wards
- [ ] Trả về danh sách xã thuộc tỉnh
- [ ] Bao gồm thông tin: id, name, code, province_id
- [ ] Implement pagination
- [ ] Implement caching
- [ ] Validate province_id exists
- [ ] Viết unit tests
- [ ] Viết API documentation
- [ ] Handle error cases

---

## Task 17: Update Whitelabel System
**Type**: Task  
**Priority**: Medium  
**Status**: To Do  
**Assignee**: [To be assigned]  

### Description
Cập nhật whitelabel system để sử dụng dữ liệu địa chỉ mới

### Acceptance Criteria
- [ ] Cập nhật whitelabel config để sử dụng API mới
- [ ] Cập nhật UI components liên quan đến địa chỉ
- [ ] Test trên tất cả whitelabel clients
- [ ] Đảm bảo backward compatibility
- [ ] Cập nhật documentation
- [ ] Deploy và monitor

---

## Task 18: Update Partner API (CPV)
**Type**: Task  
**Priority**: Medium  
**Status**: To Do  
**Assignee**: [To be assigned]  

### Description
Cập nhật API phục vụ đối tác (CPV) để sử dụng dữ liệu địa chỉ mới

### Acceptance Criteria
- [ ] Cập nhật API endpoints phục vụ CPV
- [ ] Đảm bảo data format consistency
- [ ] Thông báo và coordinate với CPV team
- [ ] Cập nhật API documentation
- [ ] Test integration với CPV system
- [ ] Monitor sau khi deploy

---

## Task 19: Update Partner API (NHANH)
**Type**: Task  
**Priority**: Medium  
**Status**: To Do  
**Assignee**: [To be assigned]  

### Description
Cập nhật API gọi sang đối tác (NHANH) để sử dụng dữ liệu địa chỉ mới

### Acceptance Criteria
- [ ] Cập nhật API calls sang NHANH
- [ ] Đảm bảo data format đúng theo yêu cầu NHANH
- [ ] Thông báo và coordinate với NHANH team
- [ ] Test integration với NHANH system
- [ ] Handle error cases và retry logic
- [ ] Monitor sau khi deploy

---

## Task 20: Update Mobile App API
**Type**: Task  
**Priority**: High  
**Status**: To Do  
**Assignee**: [To be assigned]  

### Description
Cập nhật API cho Mobile App để sử dụng dữ liệu địa chỉ mới

### Acceptance Criteria
- [ ] Cập nhật mobile API endpoints
- [ ] Đảm bảo backward compatibility cho old app versions
- [ ] Cập nhật API documentation
- [ ] Test với mobile app team
- [ ] Implement versioning nếu cần
- [ ] Monitor performance impact

---

## Task 21: Update Giftlink API
**Type**: Task  
**Priority**: Medium  
**Status**: To Do  
**Assignee**: [To be assigned]  

### Description
Cập nhật API giftlink để sử dụng dữ liệu địa chỉ mới

### Acceptance Criteria
- [ ] Cập nhật giftlink API endpoints
- [ ] Đảm bảo data consistency
- [ ] Test functionality end-to-end
- [ ] Cập nhật documentation
- [ ] Coordinate với related teams
- [ ] Monitor sau khi deploy

---

## Dependencies
- Task 4 depends on Task 3 (geometry province data)
- Task 9, 10 depend on Task 4, 5, 6, 7, 8
- Task 15, 16 depend on Task 5, 6
- Task 17, 18, 19, 20, 21 depend on Task 15, 16

## Notes
- Task 1, 2: ✅ Completed
- Task 3: 🔄 In Progress
- Tasks 4-21: 📋 To Do 