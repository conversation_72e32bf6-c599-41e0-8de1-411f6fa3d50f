#!/usr/bin/env python3
"""
Script để chuyển đổi geometry từ ESRI format sang GeoJSON format
"""

import json
import os
from typing import Dict, Any, List, Optional

def convert_esri_to_geojson_geometry(esri_geometry: Dict[str, Any]) -> Dict[str, Any]:
    """
    Chuyển đổi geometry từ ESRI format sang GeoJSON format
    
    Args:
        esri_geometry: Geometry object trong ESRI format
        
    Returns:
        Geometry object trong GeoJSON format
    """
    if 'rings' in esri_geometry:
        # Đây là polygon
        return {
            "type": "Polygon",
            "coordinates": esri_geometry['rings']
        }
    elif 'paths' in esri_geometry:
        # Đây là linestring
        return {
            "type": "LineString",
            "coordinates": esri_geometry['paths'][0] if len(esri_geometry['paths']) == 1 else esri_geometry['paths']
        }
    elif 'x' in esri_geometry and 'y' in esri_geometry:
        # Đ<PERSON><PERSON> là point
        return {
            "type": "Point",
            "coordinates": [esri_geometry['x'], esri_geometry['y']]
        }
    else:
        # Không xác định được type
        raise ValueError("Không thể xác định loại geometry")

def convert_file(input_file: str, output_file: Optional[str] = None) -> None:
    """
    Chuyển đổi một file từ ESRI format sang GeoJSON format
    
    Args:
        input_file: Đường dẫn file input
        output_file: Đường dẫn file output (nếu None sẽ ghi đè file gốc)
    """
    # Đọc file
    with open(input_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # Chuyển đổi geometry cho từng feature
    if 'features' in data:
        for feature in data['features']:
            if 'geometry' in feature:
                try:
                    feature['geometry'] = convert_esri_to_geojson_geometry(feature['geometry'])
                    print(f"✓ Đã chuyển đổi geometry cho feature {feature.get('attributes', {}).get('OBJECTID', 'N/A')}")
                except Exception as e:
                    print(f"✗ Lỗi khi chuyển đổi geometry: {e}")
    
    # Ghi file kết quả
    output_path = output_file if output_file else input_file
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    
    print(f"✓ Đã lưu file kết quả: {output_path}")

def convert_directory(input_dir: str, output_dir: Optional[str] = None) -> None:
    """
    Chuyển đổi tất cả file JSON trong một thư mục
    
    Args:
        input_dir: Thư mục chứa file input
        output_dir: Thư mục output (nếu None sẽ ghi đè thư mục gốc)
    """
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    json_files = [f for f in os.listdir(input_dir) if f.endswith('.json')]
    
    for filename in json_files:
        input_path = os.path.join(input_dir, filename)
        output_path = os.path.join(output_dir, filename) if output_dir else input_path
        
        try:
            convert_file(input_path, output_path)
            print(f"✓ Đã xử lý: {filename}")
        except Exception as e:
            print(f"✗ Lỗi khi xử lý {filename}: {e}")

def main():
    """Main function"""
    print("🔄 Chuyển đổi geometry từ ESRI format sang GeoJSON format")
    print("=" * 60)
    
    # Ví dụ chuyển đổi file hiện tại
    current_file = "crawl_ward/exports/final/individual/ward_5_Hà_Nội_Xã_Phúc_Thọ.json"
    
    if os.path.exists(current_file):
        print(f"📁 Đang chuyển đổi file: {current_file}")
        convert_file(current_file)
        print("✅ Hoàn thành!")
    else:
        print("❌ Không tìm thấy file")
    
    # Hỏi người dùng có muốn chuyển đổi toàn bộ thư mục không
    choice = input("\n🤔 Bạn có muốn chuyển đổi toàn bộ thư mục 'crawl_ward/exports/final/individual/'? (y/n): ")
    
    if choice.lower() == 'y':
        input_dir = "crawl_ward/exports/final/individual/"
        if os.path.exists(input_dir):
            print(f"📁 Đang chuyển đổi thư mục: {input_dir}")
            convert_directory(input_dir)
            print("✅ Hoàn thành tất cả!")
        else:
            print("❌ Không tìm thấy thư mục")

if __name__ == "__main__":
    main() 