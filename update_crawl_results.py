#!/usr/bin/env python3
"""
Script để update crawl_results.json với kết quả retry
"""

import json
from pathlib import Path
from datetime import datetime

def update_crawl_results_with_retry():
    """Cập nhật crawl_results.json với kết quả retry"""
    
    # Tìm session gần nhất
    crawled_data_path = Path("crawled_data")
    session_dirs = [d for d in crawled_data_path.iterdir() 
                   if d.is_dir() and d.name.startswith("crawl_session_")]
    
    if not session_dirs:
        print("Không tìm thấy session nào")
        return
    
    # Lấy session mới nhất
    latest_session = max(session_dirs, key=lambda x: x.name)
    print(f"Sử dụng session: {latest_session.name}")
    
    # Đọc file crawl_results.json
    crawl_results_file = latest_session / "crawl_results.json"
    retry_results_file = latest_session / "retry_results.json"
    
    if not crawl_results_file.exists():
        print("Không tìm thấy file crawl_results.json")
        return
    
    if not retry_results_file.exists():
        print("Không tìm thấy file retry_results.json")
        return
    
    # Đ<PERSON><PERSON> dữ liệu
    with open(crawl_results_file, 'r', encoding='utf-8') as f:
        crawl_results = json.load(f)
    
    with open(retry_results_file, 'r', encoding='utf-8') as f:
        retry_results = json.load(f)
    
    print(f"Crawl results trước: {crawl_results['success_count']}/{crawl_results['total_provinces']} thành công")
    print(f"Retry results: {retry_results['success_count']}/{retry_results['total_retries']} thành công")
    
    # Tạo dictionary để lookup nhanh
    province_lookup = {
        result['province_code']: result 
        for result in crawl_results['results']
    }
    
    # Cập nhật các province từ retry
    updated_count = 0
    for retry_result in retry_results['results']:
        province_code = retry_result['province_code']
        
        if province_code in province_lookup:
            if retry_result['status'] == 'success':
                # Cập nhật thành công
                province_lookup[province_code].update({
                    'status': 'success',
                    'features_count': retry_result['features_count'],
                    'file_path': retry_result['file_path']
                })
                # Xóa error field nếu có
                if 'error' in province_lookup[province_code]:
                    del province_lookup[province_code]['error']
                updated_count += 1
                print(f"✓ Cập nhật {retry_result['province_name']} thành công")
            else:
                # Cập nhật lỗi mới
                province_lookup[province_code].update({
                    'status': 'error',
                    'error': retry_result['error'],
                    'features_count': 0
                })
                print(f"✗ Cập nhật {retry_result['province_name']} vẫn lỗi")
    
    # Tính lại success_count và error_count
    success_count = sum(1 for result in crawl_results['results'] if result['status'] == 'success')
    error_count = sum(1 for result in crawl_results['results'] if result['status'] == 'error')
    
    # Cập nhật timestamp và counts
    crawl_results.update({
        'timestamp': datetime.now().isoformat(),
        'success_count': success_count,
        'error_count': error_count,
        'last_retry': retry_results['timestamp']
    })
    
    # Lưu lại file
    with open(crawl_results_file, 'w', encoding='utf-8') as f:
        json.dump(crawl_results, f, ensure_ascii=False, indent=2)
    
    print(f"\n📊 Đã cập nhật crawl_results.json:")
    print(f"   - Provinces updated: {updated_count}")
    print(f"   - Success: {success_count}/{crawl_results['total_provinces']}")
    print(f"   - Error: {error_count}/{crawl_results['total_provinces']}")
    print(f"   - Tỷ lệ thành công: {success_count/crawl_results['total_provinces']*100:.1f}%")

if __name__ == "__main__":
    update_crawl_results_with_retry() 