#!/usr/bin/env python3
"""
VNSDI Toolkit - Tool hoàn chỉnh cho việc crawl dữ liệu VNSDI
Tích hợp: Crawl → Retry → Export CSV → Create JIRA Task
"""

import json
import os
import sys
import time
from datetime import datetime
from pathlib import Path
import subprocess
import shutil

# Import các module từ các file khác
from crawl_vnsdi_data import VNSDIDataCrawler

class VNSDIToolkit:
    def __init__(self):
        self.current_session = None
        self.crawled_data_path = Path("crawled_data")
        self.setup_directories()
        
    def setup_directories(self):
        """Tạ<PERSON> các thư mục cần thiết"""
        self.crawled_data_path.mkdir(exist_ok=True)
        
    def print_header(self):
        """In header của tool"""
        print("=" * 70)
        print("🚀 VNSDI TOOLKIT - CÔNG CỤ CRAWL DỮ LIỆU VNSDI HOÀN CHỈNH")
        print("=" * 70)
        print("📍 Crawl dữ liệu đơn vị hành chính Việt Nam từ VNSDI")
        print("🔄 Tích hợp: Crawl → Retry → Export CSV → Create JIRA Task")
        print("=" * 70)
        
    def print_menu(self):
        """In menu chính"""
        print("\n🎯 MENU CHÍNH:")
        print("1. 🔄 Crawl dữ liệu ban đầu")
        print("2. 🔁 Retry các tỉnh failed")
        print("3. 📊 Export dữ liệu to CSV")
        print("4. 📋 Tạo JIRA task")
        print("5. 📈 Xem báo cáo tổng quan")
        print("6. 🔧 Chọn session làm việc")
        print("0. ❌ Thoát")
        print("-" * 50)
        
    def get_latest_session(self):
        """Lấy session gần nhất"""
        session_dirs = [d for d in self.crawled_data_path.iterdir() 
                       if d.is_dir() and d.name.startswith("crawl_session_")]
        
        if not session_dirs:
            return None
            
        return max(session_dirs, key=lambda x: x.name)
        
    def select_session(self):
        """Chọn session để làm việc"""
        session_dirs = [d for d in self.crawled_data_path.iterdir() 
                       if d.is_dir() and d.name.startswith("crawl_session_")]
        
        if not session_dirs:
            print("❌ Không tìm thấy session nào!")
            return None
        
        # Sắp xếp theo thời gian
        session_dirs.sort(key=lambda x: x.name, reverse=True)
        
        print("\n📁 Danh sách sessions:")
        for i, session_dir in enumerate(session_dirs, 1):
            print(f"{i}. {session_dir.name}")
            
        try:
            choice = int(input("\nChọn session (số): "))
            if 1 <= choice <= len(session_dirs):
                return session_dirs[choice - 1]
            else:
                print("❌ Lựa chọn không hợp lệ!")
                return None
        except ValueError:
            print("❌ Vui lòng nhập số!")
            return None
            
    def crawl_initial_data(self):
        """Crawl dữ liệu ban đầu"""
        print("\n🔄 BẮT ĐẦU CRAWL DỮ LIỆU BAN ĐẦU")
        print("=" * 50)
        
        # Chạy crawl_from_province_file.py
        try:
            result = subprocess.run([sys.executable, "crawl_from_province_file.py"], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ Crawl dữ liệu ban đầu thành công!")
                
                # Tự động chọn session mới nhất
                self.current_session = self.get_latest_session()
                if self.current_session:
                    print(f"📁 Session hiện tại: {self.current_session.name}")
                    
                    # Hiển thị kết quả
                    self.show_crawl_summary()
                    
                    # Hỏi có muốn retry không
                    if input("\n🔁 Có muốn retry các tỉnh failed không? (y/n): ").lower() == 'y':
                        self.retry_failed_provinces()
                        
            else:
                print(f"❌ Lỗi crawl: {result.stderr}")
                
        except Exception as e:
            print(f"❌ Lỗi chạy crawl: {e}")
            
    def retry_failed_provinces(self):
        """Retry các tỉnh failed"""
        print("\n🔁 BẮT ĐẦU RETRY CÁC TỈNH FAILED")
        print("=" * 50)
        
        if not self.current_session:
            self.current_session = self.get_latest_session()
            if not self.current_session:
                print("❌ Không tìm thấy session nào!")
                return
        
        # Import và sử dụng VNSDIRetryTool
        from retry_failed_provinces import VNSDIRetryTool
        
        try:
            retry_tool = VNSDIRetryTool(self.current_session)
            retry_summary = retry_tool.run_retry()
            
            if retry_summary:
                print(f"\n✅ Retry hoàn thành!")
                print(f"📊 Kết quả: {retry_summary['success_count']}/{retry_summary['total_retries']} thành công")
                
                # Tự động cập nhật crawl_results.json (đã tích hợp trong retry_tool)
                print("📝 Crawl results đã được cập nhật tự động!")
                
                # Hiển thị báo cáo mới
                self.show_crawl_summary()
                
                # Hỏi có muốn export CSV không
                if input("\n📊 Có muốn export dữ liệu to CSV không? (y/n): ").lower() == 'y':
                    self.export_to_csv()
                    
        except Exception as e:
            print(f"❌ Lỗi retry: {e}")
            
    def export_to_csv(self):
        """Export dữ liệu to CSV"""
        print("\n📊 BẮT ĐẦU EXPORT DỮ LIỆU TO CSV")
        print("=" * 50)
        
        if not self.current_session:
            self.current_session = self.get_latest_session()
            if not self.current_session:
                print("❌ Không tìm thấy session nào!")
                return
        
        print("🎯 Chọn loại export:")
        print("1. Export tổng hợp (tất cả tỉnh trong 1 file CSV)")
        print("2. Export thống kê (thông tin tổng quan từng tỉnh)")
        print("3. Export theo tỉnh (mỗi tỉnh 1 file CSV)")
        print("4. Export tất cả (cả 3 loại trên)")
        
        try:
            choice = input("\nNhập lựa chọn (1-4): ").strip()
            
            # Chạy export_to_csv.py với input tự động
            process = subprocess.Popen([sys.executable, "export_to_csv.py"], 
                                     stdin=subprocess.PIPE, 
                                     stdout=subprocess.PIPE, 
                                     stderr=subprocess.PIPE, 
                                     text=True)
            
            stdout, stderr = process.communicate(input=choice)
            
            if process.returncode == 0:
                print("✅ Export CSV thành công!")
                print(stdout)
                
                # Hỏi có muốn tạo JIRA task không
                if input("\n📋 Có muốn tạo JIRA task không? (y/n): ").lower() == 'y':
                    self.create_jira_task()
                    
            else:
                print(f"❌ Lỗi export: {stderr}")
                
        except Exception as e:
            print(f"❌ Lỗi chạy export: {e}")
            
    def create_jira_task(self):
        """Tạo JIRA task"""
        print("\n📋 BẮT ĐẦU TẠO JIRA TASK")
        print("=" * 50)
        
        if not self.current_session:
            self.current_session = self.get_latest_session()
            if not self.current_session:
                print("❌ Không tìm thấy session nào!")
                return
        
        try:
            result = subprocess.run([sys.executable, "jira_task_vietnamese.py"], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ Tạo JIRA task thành công!")
                print(result.stdout)
            else:
                print(f"❌ Lỗi tạo JIRA task: {result.stderr}")
                
        except Exception as e:
            print(f"❌ Lỗi chạy JIRA task: {e}")
            
    def show_crawl_summary(self):
        """Hiển thị báo cáo tổng quan"""
        if not self.current_session:
            self.current_session = self.get_latest_session()
            if not self.current_session:
                print("❌ Không tìm thấy session nào!")
                return
        
        print(f"\n📈 BÁO CÁO TỔNG QUAN - SESSION: {self.current_session.name}")
        print("=" * 60)
        
        # Đọc crawl_results.json
        crawl_results_file = self.current_session / "crawl_results.json"
        
        if not crawl_results_file.exists():
            print("❌ Không tìm thấy file crawl_results.json")
            return
        
        try:
            with open(crawl_results_file, 'r', encoding='utf-8') as f:
                crawl_results = json.load(f)
            
            success_count = crawl_results['success_count']
            total_provinces = crawl_results['total_provinces']
            error_count = crawl_results['error_count']
            
            # Tính tổng số features
            total_features = sum(
                result.get('features_count', 0) 
                for result in crawl_results['results'] 
                if result['status'] == 'success'
            )
            
            # Thống kê cơ bản
            print(f"📊 THỐNG KÊ TỔNG QUAN:")
            print(f"   ✅ Tỉnh/thành thành công: {success_count}/{total_provinces}")
            print(f"   ❌ Tỉnh/thành thất bại: {error_count}/{total_provinces}")
            print(f"   📈 Tỷ lệ thành công: {success_count/total_provinces*100:.1f}%")
            print(f"   🏘️  Tổng số đơn vị hành chính: {total_features:,}")
            
            # Thống kê theo vùng
            print(f"\n🌍 THỐNG KÊ THEO VÙNG:")
            
            # Phân loại tỉnh failed
            failed_provinces = [
                result for result in crawl_results['results'] 
                if result['status'] == 'error'
            ]
            
            if failed_provinces:
                print(f"\n❌ CÁC TỈNH THẤT BẠI ({len(failed_provinces)}):")
                for province in failed_provinces:
                    print(f"   • {province['province_name']} (Code: {province['province_code']})")
                    print(f"     Lỗi: {province.get('error', 'Unknown')}")
            
            # Thống kê top tỉnh nhiều đơn vị nhất
            successful_provinces = [
                result for result in crawl_results['results'] 
                if result['status'] == 'success'
            ]
            
            if successful_provinces:
                top_provinces = sorted(successful_provinces, 
                                     key=lambda x: x.get('features_count', 0), 
                                     reverse=True)[:5]
                
                print(f"\n🏆 TOP 5 TỈNH NHIỀU ĐƠN VỊ NHẤT:")
                for i, province in enumerate(top_provinces, 1):
                    print(f"   {i}. {province['province_name']}: {province['features_count']} đơn vị")
            
            # Thông tin session
            print(f"\n📁 THÔNG TIN SESSION:")
            print(f"   📅 Thời gian: {crawl_results.get('timestamp', 'Unknown')}")
            if 'last_retry' in crawl_results:
                print(f"   🔄 Retry gần nhất: {crawl_results['last_retry']}")
            
        except Exception as e:
            print(f"❌ Lỗi đọc báo cáo: {e}")
            
    def run_complete_workflow(self):
        """Chạy workflow hoàn chỉnh"""
        print("\n🚀 BẮT ĐẦU WORKFLOW HOÀN CHỈNH")
        print("=" * 50)
        
        # 1. Crawl ban đầu
        print("🔄 Bước 1: Crawl dữ liệu ban đầu...")
        self.crawl_initial_data()
        
        if not self.current_session:
            print("❌ Không thể tiếp tục workflow!")
            return
        
        # 2. Retry tự động
        print("\n🔁 Bước 2: Retry các tỉnh failed...")
        self.retry_failed_provinces()
        
        # 3. Export CSV
        print("\n📊 Bước 3: Export dữ liệu to CSV...")
        # Tự động chọn export tất cả
        process = subprocess.Popen([sys.executable, "export_to_csv.py"], 
                                 stdin=subprocess.PIPE, 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE, 
                                 text=True)
        
        stdout, stderr = process.communicate(input="4")  # Export tất cả
        
        if process.returncode == 0:
            print("✅ Export CSV thành công!")
        else:
            print(f"❌ Lỗi export: {stderr}")
        
        # 4. Tạo JIRA task
        print("\n📋 Bước 4: Tạo JIRA task...")
        self.create_jira_task()
        
        # 5. Báo cáo cuối
        print("\n📈 Bước 5: Báo cáo tổng kết...")
        self.show_crawl_summary()
        
        print("\n🎉 WORKFLOW HOÀN CHỈNH THÀNH CÔNG!")
        
    def run(self):
        """Chạy tool chính"""
        self.print_header()
        
        # Tự động chọn session gần nhất
        self.current_session = self.get_latest_session()
        if self.current_session:
            print(f"📁 Session hiện tại: {self.current_session.name}")
        else:
            print("💡 Chưa có session nào. Hãy chạy crawl dữ liệu ban đầu.")
        
        while True:
            self.print_menu()
            
            try:
                choice = input("Nhập lựa chọn (0-6): ").strip()
                
                if choice == "0":
                    print("👋 Tạm biệt!")
                    break
                elif choice == "1":
                    self.crawl_initial_data()
                elif choice == "2":
                    self.retry_failed_provinces()
                elif choice == "3":
                    self.export_to_csv()
                elif choice == "4":
                    self.create_jira_task()
                elif choice == "5":
                    self.show_crawl_summary()
                elif choice == "6":
                    selected_session = self.select_session()
                    if selected_session:
                        self.current_session = selected_session
                        print(f"✅ Đã chọn session: {selected_session.name}")
                elif choice.lower() == "auto":
                    # Hidden option: chạy workflow hoàn chỉnh
                    self.run_complete_workflow()
                else:
                    print("❌ Lựa chọn không hợp lệ!")
                    
            except KeyboardInterrupt:
                print("\n\n👋 Tạm biệt!")
                break
            except Exception as e:
                print(f"❌ Lỗi: {e}")
                
            # Pause trước khi hiển thị menu lại
            if choice != "0":
                input("\nNhấn Enter để tiếp tục...")

def main():
    """Main function"""
    toolkit = VNSDIToolkit()
    toolkit.run()

if __name__ == "__main__":
    main() 